package com.knet.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.knet.common.dto.message.InventoryFailedMessage;
import com.knet.common.dto.message.InventoryLockSuccessMessage;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.order.mapper.SysOrderItemMapper;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.service.IInventoryConsumerService;
import com.knet.order.service.ISysOrderItemService;
import com.knet.order.service.ISysOrderProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/17 14:01
 * @description: 消费库存消息服务实现
 */
@Slf4j
@Service
public class InventoryConsumerServiceImpl implements IInventoryConsumerService {

    @Resource
    private SysOrderItemMapper sysOrderItemMapper;
    @Resource
    private ISysOrderItemService sysOrderItemService;

    /**
     * 验证oneId唯一性约束
     * 除了状态6（CANCELLED）和8（SYSTEM_CANCELLED）之外的状态，同一个oneId只能对应一个订单项数据
     *
     * @param oneId         要检查的oneId
     * @param excludeItemId 排除的订单项ID（用于更新场景）
     * @param currentOrderId 当前订单ID（用于排除同一订单的其他订单项）
     * @throws ServiceException 如果违反唯一性约束
     */
    private void validateOneIdUniqueness(String oneId, Long excludeItemId, String currentOrderId) {
        if (oneId == null || oneId.isEmpty()) {
            return;
        }
        LambdaQueryWrapper<SysOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(SysOrderItem::getOneId, oneId)
                .notIn(SysOrderItem::getStatus, KnetOrderItemStatus.CANCELLED, KnetOrderItemStatus.SYSTEM_CANCELLED);
        if (excludeItemId != null) {
            queryWrapper.ne(SysOrderItem::getItemId, excludeItemId);
        }

        // 排除当前订单的所有订单项，避免MQ重复消费时的冲突
        if (currentOrderId != null && !currentOrderId.isEmpty()) {
            queryWrapper.ne(SysOrderItem::getParentOrderId, currentOrderId);
        }
        long count = sysOrderItemService.count(queryWrapper);
        if (count > 0) {
            log.error("oneId唯一性约束违反: oneId={}, 已存在{}个非取消状态的订单项（排除当前订单{}）", oneId, count, currentOrderId);
            throw new ServiceException("商品oneId " + oneId + " 已被其他订单占用，无法重复分配");
        }

        log.debug("oneId唯一性检查通过: oneId={}, excludeItemId={}, currentOrderId={}", oneId, excludeItemId, currentOrderId);
    }

    @Resource
    private ISysOrderProcessService orderProcessService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processInventoryFailed(String messageBody) {
        InventoryFailedMessage failedEvent = JSON.parseObject(messageBody, InventoryFailedMessage.class);
        log.info("订单服务处理库存扣减失败补偿: {}", messageBody);
        try {
            String orderId = failedEvent.getOrderId();
            orderProcessService.cancelOrder(orderId);
            // 清除用户订单缓存
            clearUserOrderListCache(failedEvent.getUserId());
            log.info("库存扣减失败补偿处理完成，订单状态已更新为已取消: orderId={}, reason={}", orderId, failedEvent.getFailureReason());
        } catch (Exception e) {
            log.error("处理库存扣减失败补偿异常: orderId={}, error={}", failedEvent.getOrderId(), e.getMessage());
            throw new ServiceException("处理库存扣减失败补偿异常: " + e.getMessage());
        }
    }

    /**
     * 清除用户订单列表缓存
     * 使用RedisCacheUtil的deleteByPattern方法进行模糊匹配删除
     */
    private void clearUserOrderListCache(Long userId) {
        try {
            // 构建缓存key模式，匹配该用户的所有订单列表缓存
            String cacheKeyPattern = "order-service:orderList:" + userId + ":*";
            // 使用RedisCacheUtil的deleteByPattern方法进行模糊匹配删除
            RedisCacheUtil.deleteByPattern(cacheKeyPattern);
            log.info("已清除用户订单列表缓存，用户ID: {}, 缓存key模式: {}", userId, cacheKeyPattern);
        } catch (Exception e) {
            log.warn("清除用户订单列表缓存失败，用户ID: {}, 错误: {}", userId, e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processInventoryLockSuccess(InventoryLockSuccessMessage lockSuccessMessage) {
        String orderId = lockSuccessMessage.getOrderId();
        Long userId = lockSuccessMessage.getUserId();
        List<InventoryLockSuccessMessage.LockedProductInfo> lockedProducts = lockSuccessMessage.getLockedProducts();
        log.info("开始处理库存锁定成功消息: orderId={}, userId={}, 锁定商品数量={}",
                orderId, userId, lockedProducts != null ? lockedProducts.size() : 0);
        if (CollUtil.isEmpty(lockedProducts)) {
            log.warn("锁定商品列表为空: orderId={}", orderId);
            return;
        }
        for (InventoryLockSuccessMessage.LockedProductInfo lockedProduct : lockedProducts) {
            try {
                updateOrderItemsWithLockInfo(orderId, lockedProduct);
            } catch (ServiceException e) {
                // 如果是oneId唯一性约束违反，可能是重复消费，记录警告但不中断处理
                if (e.getMessage().contains("已被其他订单占用")) {
                    log.warn("订单项oneId可能已存在，跳过更新: orderId={}, sku={}, size={}, error={}",
                            orderId, lockedProduct.getSku(), lockedProduct.getSize(), e.getMessage());
                    continue;
                }
                throw e; // 其他异常继续抛出
            }
        }
        log.info("库存锁定成功消息处理完成: orderId={}", orderId);
    }

    /**
     * 更新订单项的锁定信息
     *
     * @param orderId       订单ID
     * @param lockedProduct 锁定的商品信息
     */
    private void updateOrderItemsWithLockInfo(String orderId, InventoryLockSuccessMessage.LockedProductInfo lockedProduct) {
        String sku = lockedProduct.getSku();
        String size = lockedProduct.getSize();
        String priceStr = lockedProduct.getPrice();
        List<InventoryLockSuccessMessage.ProductDetail> productDetails = lockedProduct.getProductDetails();
        if (CollUtil.isEmpty(productDetails)) {
            log.warn("商品详情列表为空: orderId={}, sku={}, size={}", orderId, sku, size);
            return;
        }
        // 将价格字符串转换为BigDecimal
        BigDecimal price = new BigDecimal(priceStr);
        // 查询匹配的订单项
        LambdaQueryWrapper<SysOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(SysOrderItem::getParentOrderId, orderId)
                .eq(SysOrderItem::getSku, sku)
                .eq(SysOrderItem::getSize, size)
                .eq(SysOrderItem::getPrice, price)
                .orderByAsc(SysOrderItem::getItemId);
        List<SysOrderItem> orderItems = sysOrderItemMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(orderItems)) {
            log.warn("未找到匹配的订单项: orderId={}, sku={}, size={}, price={}",
                    orderId, sku, size, price);
            return;
        }
        if (orderItems.size() != productDetails.size()) {
            log.warn("订单项数量与锁定商品数量不匹配: orderId={}, sku={}, size={}, 订单项数量={}, 锁定商品数量={}",
                    orderId, sku, size, orderItems.size(), productDetails.size());
        }
        // 按顺序更新订单项的oneId和knetListingId
        int updateCount = Math.min(orderItems.size(), productDetails.size());
        for (int i = 0; i < updateCount; i++) {
            SysOrderItem orderItem = orderItems.get(i);
            InventoryLockSuccessMessage.ProductDetail productDetail = productDetails.get(i);

            // 检查订单项是否已经有了正确的oneId（幂等性检查）
            if (productDetail.getOneId().equals(orderItem.getOneId()) &&
                productDetail.getKnetListingId().equals(orderItem.getKnetListingId())) {
                log.info("订单项已有正确的商品信息，跳过更新: itemId={}, oneId={}, knetListingId={}",
                        orderItem.getItemId(), productDetail.getOneId(), productDetail.getKnetListingId());
                continue;
            }

            // 验证oneId唯一性约束（排除当前订单项和当前订单）
            validateOneIdUniqueness(productDetail.getOneId(), orderItem.getItemId(), orderItem.getParentOrderId());
            LambdaUpdateWrapper<SysOrderItem> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(SysOrderItem::getItemId, orderItem.getItemId())
                    .set(SysOrderItem::getOneId, productDetail.getOneId())
                    .set(SysOrderItem::getKnetListingId, productDetail.getKnetListingId())
                    .set(SysOrderItem::getWarehouse, productDetail.getWarehouse())
                    .set(SysOrderItem::getSource, productDetail.getSource());
            int updated = sysOrderItemMapper.update(null, updateWrapper);
            if (updated > 0) {
                log.info("订单项锁定信息更新成功: itemId={}, oneId={}, knetListingId={}, warehouse={},source={}",
                        orderItem.getItemId(), productDetail.getOneId(), productDetail.getKnetListingId(), productDetail.getWarehouse(), productDetail.getSource());
            } else {
                log.error("订单项锁定信息更新失败: itemId={}, oneId={}, knetListingId={}, warehouse={},source={}",
                        orderItem.getItemId(), productDetail.getOneId(), productDetail.getKnetListingId(), productDetail.getWarehouse(), productDetail.getSource());
            }
        }
    }
}
